<template>
  <view class="tabs">
    <scroll-view
      ref="tabbarRef"
      id="tab-bar"
      class="tab-bar"
      :scroll="false"
      :scroll-x="true"
      :show-scrollbar="false"
      :scroll-into-view="scrollInto"
    >
      <view style="flex-direction: column;">
        <view style="flex-direction: row;">
          <view
            class="uni-tab-item"
            v-for="(tab, index) in tabList"
            :key="tab.id"
            :id="tab.id"
            :ref="el => setTabItemRef(el, index)"
            :data-id="index"
            :data-current="index"
            @click="onTabTap"
          >
            <text
              class="uni-tab-item-title"
              :class="tabIndex === index ? 'uni-tab-item-title-active' : ''"
            >
              {{ tab.name }}
            </text>
          </view>
        </view>
        <view class="scroll-view-indicator">
          <view
            ref="underlineRef"
            class="scroll-view-underline"
            :class="isTap ? 'scroll-view-animation' : ''"
            :style="{ left: indicatorLineLeft + 'px', width: indicatorLineWidth + 'px' }"
          ></view>
        </view>
      </view>
    </scroll-view>
    <view class="tab-bar-line"></view>
    <swiper
      class="tab-view"
      ref="swiperRef"
      id="tab-bar-view"
      :current="tabIndex"
      :duration="300"
      @change="onSwiperChange"
      @transition="onSwiperScroll"
      @animationfinish="animationFinish"
      @onAnimationEnd="animationFinish"
    >
      <swiper-item class="swiper-item" v-for="(page, index) in tabList" :key="index">
        <!-- #ifndef MP-ALIPAY -->
        <SwiperPage
          class="swiper-page"
          :pid="page.pageid"
          :ref="el => setPageRef(el, index)"
        />
        <!-- #endif -->
        <!-- #ifdef MP-ALIPAY -->
        <SwiperPage
          class="swiper-page"
          :pid="page.pageid"
          :ref="el => setPageRef(el, index, 'page' + index)"
        />
        <!-- #endif -->
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, getCurrentInstance } from 'vue'
import SwiperPage from './swiper-page.vue'

// #ifdef APP-NVUE
const dom = weex.requireModule('dom')
// #endif

// 缓存每页最多
const MAX_CACHE_DATA = 100
// 缓存页签数量
const MAX_CACHE_PAGE = 3
const TAB_PRELOAD_OFFSET = 1

// Props
const props = defineProps({
  tabs: {
    type: Array,
    default: () => []
  },
  defaultIndex: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['change', 'tabClick'])

// Refs
const tabbarRef = ref(null)
const swiperRef = ref(null)
const underlineRef = ref(null)
const tabItemRefs = ref([])
const pageRefs = ref([])

// Reactive data
const tabList = ref([])
const tabIndex = ref(0)
const cacheTab = ref([])
const scrollInto = ref('')
const indicatorLineLeft = ref(0)
const indicatorLineWidth = ref(0)
const isTap = ref(false)

// Internal variables
let _lastTabIndex = 0
let swiperWidth = 0
let tabbarWidth = 0
let tabListSize = {}
let _touchTabIndex = 0

// Helper functions for refs
const setTabItemRef = (el, index) => {
  if (el) {
    tabItemRefs.value[index] = el
  }
}

const setPageRef = (el, index, refName = null) => {
  if (el) {
    pageRefs.value[index] = el
  }
}

// Initialize data
const initTabList = () => {
  if (props.tabs && props.tabs.length > 0) {
    tabList.value = props.tabs.map((tab, index) => ({
      id: tab.id || `tab${index}`,
      name: tab.name || `Tab ${index + 1}`,
      pageid: tab.pageid || index + 1,
      ...tab
    }))
  } else {
    // Default tabs for demo
    for (let i = 0; i < 6; i++) {
      tabList.value.push({
        id: `tab${i}`,
        name: `Tab ${i + 1}`,
        pageid: i + 1
      })
    }
  }
  tabIndex.value = props.defaultIndex
}

// Methods
const onTabTap = (e) => {
  let index = e.target.dataset.current || e.currentTarget.dataset.current

  // #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-QQ
  isTap.value = true
  const currentSize = tabListSize[index]
  updateIndicator(currentSize.left, currentSize.width)
  _touchTabIndex = index
  // #endif

  switchTab(index)
  emit('tabClick', { index, tab: tabList.value[index] })
}

const onSwiperChange = (e) => {
  // 注意：百度小程序会触发2次
  // #ifndef APP-PLUS || H5 || MP-WEIXIN || MP-QQ
  let index = e.target.current || e.detail.current
  switchTab(index)
  // #endif
}

const onSwiperScroll = (e) => {
  if (isTap.value) {
    return
  }

  const offsetX = e.detail.dx
  let preloadIndex = _lastTabIndex
  if (offsetX > TAB_PRELOAD_OFFSET) {
    preloadIndex++
  } else if (offsetX < -TAB_PRELOAD_OFFSET) {
    preloadIndex--
  }
  if (preloadIndex === _lastTabIndex || preloadIndex < 0 || preloadIndex > pageRefs.value.length - 1) {
    return
  }
  if (pageRefs.value[preloadIndex] && pageRefs.value[preloadIndex].dataList.length === 0) {
    loadTabData(preloadIndex)
  }

  // 计算 tabbar 底线
  // #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-QQ
  const percentage = Math.abs(swiperWidth / offsetX)
  const currentSize = tabListSize[_lastTabIndex]
  const preloadSize = tabListSize[preloadIndex]
  const lineL = currentSize.left + (preloadSize.left - currentSize.left) / percentage
  const lineW = currentSize.width + (preloadSize.width - currentSize.width) / percentage
  updateIndicator(lineL, lineW)
  // #endif
}

const animationFinish = (e) => {
  // #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-QQ
  let index = e.detail.current
  if (_touchTabIndex === index) {
    isTap.value = false
  }
  _lastTabIndex = index
  switchTab(index)
  updateIndicator(tabListSize[index].left, tabListSize[index].width)
  // #endif
}

const getTabbarItemsSize = () => {
  // #ifdef APP-NVUE
  // 查询 tabbar 宽度
  uni.createSelectorQuery().in(getCurrentInstance()).select('#tab-bar').boundingClientRect().exec(rect => {
    tabbarWidth = rect[0].width
  })
  // 查询 tabview 宽度
  uni.createSelectorQuery().in(getCurrentInstance()).select('#tab-bar-view').boundingClientRect().exec(rect => {
    swiperWidth = rect[0].width
  })

  // 因 nvue 暂不支持 class 查询
  const queryTabSize = uni.createSelectorQuery().in(getCurrentInstance())
  for (let i = 0; i < tabList.value.length; i++) {
    queryTabSize.select('#' + tabList.value[i].id).boundingClientRect()
  }
  queryTabSize.exec(rects => {
    console.log(JSON.stringify(rects))
    rects.forEach((rect) => {
      tabListSize[rect.dataset.id] = rect
    })
  })
  // #endif

  // #ifdef MP-WEIXIN || H5 || MP-QQ
  uni.createSelectorQuery().in(getCurrentInstance()).select('.tab-view').fields({
    dataset: true,
    size: true,
  }, (res) => {
    swiperWidth = res.width
  }).exec()
  uni.createSelectorQuery().in(getCurrentInstance()).selectAll('.uni-tab-item').boundingClientRect((rects) => {
    rects.forEach((rect) => {
      tabListSize[rect.dataset.id] = rect
    })
  }).exec()
  // #endif

  // #ifdef APP-NVUE || H5 || MP-WEIXIN || MP-QQ
  setTimeout(() => {
    updateIndicator(tabListSize[tabIndex.value].left, tabListSize[tabIndex.value].width)
  }, 100)
  // #endif
}

const updateIndicator = (left, width) => {
  indicatorLineLeft.value = left
  indicatorLineWidth.value = width
}

const switchTab = (index) => {
  if (pageRefs.value[index] && pageRefs.value[index].dataList.length === 0) {
    loadTabData(index)
  }

  if (tabIndex.value === index) {
    return
  }

  // 缓存 tabId
  if (pageRefs.value[tabIndex.value] && pageRefs.value[tabIndex.value].dataList.length > MAX_CACHE_DATA) {
    const isExist = cacheTab.value.indexOf(tabIndex.value)
    if (isExist < 0) {
      cacheTab.value.push(tabIndex.value)
    }
  }

  tabIndex.value = index

  // #ifdef APP-NVUE
  scrollTabTo(index)
  // #endif
  // #ifndef APP-NVUE
  scrollInto.value = tabList.value[index].id
  // #endif

  // 释放 tabId
  if (cacheTab.value.length > MAX_CACHE_PAGE) {
    const cacheIndex = cacheTab.value[0]
    clearTabData(cacheIndex)
    cacheTab.value.splice(0, 1)
  }

  emit('change', { index, tab: tabList.value[index] })
}

const scrollTabTo = (index) => {
  const el = tabItemRefs.value[index]
  let offset = 0
  // TODO fix ios offset
  if (index > 0) {
    offset = tabbarWidth / 2 - tabListSize[index].width / 2
    if (tabListSize[index].right < tabbarWidth / 2) {
      offset = tabListSize[0].width
    }
  }
  dom.scrollToElement(el, {
    offset: -offset
  })
}

const loadTabData = (index) => {
  if (pageRefs.value[index] && pageRefs.value[index].loadData) {
    pageRefs.value[index].loadData()
  }
}

const clearTabData = (index) => {
  if (pageRefs.value[index] && pageRefs.value[index].clear) {
    pageRefs.value[index].clear()
  }
}

// Lifecycle hooks
onMounted(() => {
  initTabList()

  nextTick(() => {
    _lastTabIndex = 0
    swiperWidth = 0
    tabbarWidth = 0
    tabListSize = {}
    _touchTabIndex = 0

    switchTab(tabIndex.value)
    getTabbarItemsSize()
  })
})

// Expose methods for parent component
defineExpose({
  switchTab,
  loadTabData,
  clearTabData,
  getTabbarItemsSize
})
</script>

<style>
/* #ifndef APP-PLUS */
page {
  width: 100%;
  min-height: 100%;
  display: flex;
}
/* #endif */

.tabs {
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  background-color: #ffffff;
  /* #ifdef MP-ALIPAY || MP-BAIDU */
  height: 100vh;
  /* #endif */
}

.tab-bar {
  width: 750rpx;
  height: 84rpx;
  flex-direction: row;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
}

/* #ifndef APP-NVUE */
.tab-bar ::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* #endif */

.scroll-view-indicator {
  position: relative;
  height: 2px;
  background-color: transparent;
}

.scroll-view-underline {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 0;
  background-color: #007AFF;
}

.scroll-view-animation {
  transition-duration: 0.2s;
  transition-property: left;
}

.tab-bar-line {
  height: 1rpx;
  background-color: #cccccc;
}

.tab-view {
  flex: 1;
}

.uni-tab-item {
  /* #ifndef APP-PLUS */
  display: inline-block;
  /* #endif */
  flex-wrap: nowrap;
  padding-left: 25px;
  padding-right: 25px;
}

.uni-tab-item-title {
  color: #555;
  font-size: 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  flex-wrap: nowrap;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
}

.uni-tab-item-title-active {
  color: #007AFF;
}

.swiper-item {
  flex: 1;
  flex-direction: column;
}

.swiper-page {
  flex: 1;
  flex-direction: row;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
</style>