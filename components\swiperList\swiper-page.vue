<template>
  <view class="uni-swiper-page">
    <view class="page-content">
      <text class="page-title">Tab View {{ pid }}</text>
      <view class="content-list" v-if="dataList.length > 0">
        <view 
          class="list-item" 
          v-for="(item, index) in dataList" 
          :key="index"
        >
          <text class="item-text">{{ item.title || `Item ${index + 1}` }}</text>
        </view>
      </view>
      <view class="empty-state" v-else>
        <text class="empty-text">暂无数据</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

// Props
const props = defineProps({
  pid: {
    type: [Number, String],
    default: ''
  },
  data: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['load', 'clear'])

// Reactive data
const dataList = ref([])
const loading = ref(false)

// Methods
const loadData = () => {
  // 首次激活时被调用
  if (dataList.value.length === 0) {
    loading.value = true
    
    // 模拟异步加载数据
    setTimeout(() => {
      // 如果有传入的数据，使用传入的数据
      if (props.data && props.data.length > 0) {
        dataList.value = [...props.data]
      } else {
        // 否则生成模拟数据
        const mockData = []
        for (let i = 0; i < 20; i++) {
          mockData.push({
            id: i,
            title: `Tab ${props.pid} - Item ${i + 1}`,
            content: `这是第${i + 1}项内容`
          })
        }
        dataList.value = mockData
      }
      loading.value = false
      emit('load', { pid: props.pid, data: dataList.value })
    }, 300)
  }
}

const clear = () => {
  // 释放数据时被调用，参考 swiper-list 缓存配置
  dataList.value.length = 0
  loading.value = false
  emit('clear', { pid: props.pid })
}

const refresh = () => {
  // 刷新数据
  clear()
  loadData()
}

// 监听props.data变化
const updateData = (newData) => {
  if (newData && Array.isArray(newData)) {
    dataList.value = [...newData]
  }
}

// Expose methods for parent component
defineExpose({
  loadData,
  clear,
  refresh,
  updateData,
  dataList
})

// 初始化
onMounted(() => {
  if (props.data && props.data.length > 0) {
    updateData(props.data)
  }
})
</script>

<style scoped>
.uni-swiper-page {
  flex: 1;
  flex-direction: column;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: #f8f8f8;
}

.page-content {
  flex: 1;
  flex-direction: column;
  padding: 20rpx;
}

.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.content-list {
  flex: 1;
  flex-direction: column;
}

.list-item {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.item-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.empty-state {
  flex: 1;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 兼容不同平台的样式 */
/* #ifdef H5 */
.uni-swiper-page {
  height: 100%;
  overflow-y: auto;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content-list {
  height: 100%;
}
/* #endif */

/* #ifdef APP-NVUE */
.list-item {
  margin-bottom: 20px;
  padding: 30px;
}
/* #endif */
</style>
