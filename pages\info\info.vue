<template>

	<view>
		<uni-nav-bar status-bar :fixed="true" :border="false">
			<view class="flex fontSize-34  w-full  justify-center items-center">消息</view>
			<block v-slot:right>
				<uni-icons @click="toggleMenu()" type="more-filled" size="30"></uni-icons>
			</block>
		</uni-nav-bar>
		<view :style="{ height: `${windowBodyHeight}px` }" class="flex bg-white w-full">
			<view class="flex justify-between  w-full flex-col">
				<!-- 分类 -->
				<u-sticky style="background-color: #efeff4;" class="p-all-20  " offsetTop="0">
					<!-- <uni-easyinput suffixIcon="search" placeholder="搜索应用" @iconClick="iconClick"></uni-easyinput> -->
					<uni-easyinput prefixIcon="search" @change="searchAppClick" @clear="searchAppClick"
						v-model="searchInput" placeholder="搜索消息" @iconClick="searchAppClick"></uni-easyinput>
				     <view style="margin-top: 10px;">
						<!-- <uni-segmented-control :current="currentMessageType" :values="messageTypeList" style-type="button"
						active-color="#007aff" @clickItem="onMessageTypeClick" /> -->
						<view  class="flex flex-wrap">
							<view v-for="(item,index) in messageTypeList" :key="index" class="mr-20 mb-20" @click="onMessageTypeClick(index)">
								<!-- <text :class="{'text-blue':currentMessageType==index}" class="fontSize-30">{{item}}</text> -->
								<uni-tag size="normal" :text="item" :inverted="currentMessageType!=index" type="primary" />
							</view>
						</view>
					</view>
					</u-sticky>
				
				<!-- 列表内容 -->
				<scroll-view scroll-y="true" @scroll="onScroll" class="flex-1 overflow-y-auto">
					<view class="  flex  flex-col pl-20 pr-20 ">

						<block v-if="messageList && messageList.length>0">
											<uni-list class="mb-80">
												<uni-swipe-action ref="swipeActionRef">		
													<!-- <uni-list :border="true"> --><!-- 	</uni-list>	 -->
													 
														<uni-swipe-action-item  v-for="(item,index) in messageList" :key="index"
															class="border-red1" :right-options="options" @click="(e) => onEditDeleteClick(e, item, index)"
															@change="swipeChange($event, index)">			
																<uni-list-chat :avatar-circle="true" @click="readMessgeFn(item)" :clickable="true" :title="item.CTITLE" avatar="../../static/images/message.png" :note="item.CCONTENT" :time="formatDateJS(item.CDATETIME_CREATED)" :badge-text="item.CMSG_COUNT"></uni-list-chat>
														</uni-swipe-action-item>
												
												</uni-swipe-action>
											</uni-list>
										</block>
										<block v-else>
											<view class="flex mt-20 justify-center items-center h-full ">
												暂无数据
											</view>
											
										</block>


					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
import dayjs from 'dayjs'
import * as serviceApi from '@/api/index.js'
import { onPullDownRefresh } from '@dcloudio/uni-app';
// import { getCurrentUserInfoByField } from '@/utils/tools.js'
import {
	ref,
	onMounted,
	computed,
	nextTick
} from 'vue'
import {
	setStorageSync,
	getStorageSync,
	CURRENT_SERVER
} from '@/utils/Storage.js'
const isOpenAllCollapse = ref(true) // 是否所有折叠
const options = ref([
	// {
	// 	text: '编辑',
	// 	style: {
	// 		backgroundColor: '#007aff'
	// 	}
	// }, 
	{
		text: '删除',
		style: {
			backgroundColor: '#dd524d'
		}
	}])
const currentMessageType = ref(0)
const messageTypeList = ref(['全部'])
const messageTypeList_origin = ref([{CTYPE_NAME:'全部',CTYPE_NO:'all'}])
const messageList = ref([])
const navBarHeight = ref(44) // 顶部标题高度
const tabBarHeight = ref(50) // 底部菜单栏高度	
const isEdit = ref(false)
const currentLongPressTab = ref(null)
const menuCategory = ref([])
const searchInput = ref('')
const copyMessgeList = ref([])
const systemInfo = uni.getSystemInfoSync(); //系统信息
const windowHeight = computed(() => {
	//windowHeight不包含NavigationBar和TabBar的高度
	return systemInfo.windowHeight
})
const windowBodyHeight = computed(() => {
	//windowHeight不包含NavigationBar和TabBar的高度
	return systemInfo.windowHeight - navBarHeight.value - systemInfo.safeArea.top
})

// setTimeout(()=>{
// 	uni.showTabBarRedDot({
// 		  index: 0
// 	})
// },1000)
onMounted(() => {
	
	checkMenuDataBeforeLoad()
})

// 下拉刷新处理函数
onPullDownRefresh(async () => {
	console.log('下拉刷新处理函数');
	try {
		// 重新加载数据
		loadMessageTypeList()
		loadDataList()

		// 停止刷新动画（必须调用）
		setTimeout(function () {
			uni.stopPullDownRefresh({
				// success: function () {
				// 	uni.showToast({
				// 		title:'刷新成功！'
				// 	})
				// },
				// fail: function () {
				// 	uni.showToast({
				// 		title:'刷新失败！'
				// 	})
				// },
				// complete: function () {
				// 	uni.showToast({
				// 		title:'刷新完成！'
				// 	})
				// }
			});
		}, 1000);
	} catch (error) {
		console.error('刷新失败:', error);
		setTimeout(function () {
			// 失败时也要停止动画
			uni.stopPullDownRefresh();
		}, 1000);
	}
});
//////////////////////methods//////////////////////////
// 加载消息类型列表
function loadMessageTypeList(){
	let params={}
	messageTypeList.value = ['全部']
	messageTypeList_origin.value = [{CTYPE_NAME:'全部',CTYPE_NO:'all'}]
	serviceApi.getNoticeType().then(res => {
		if (res && res.data.code === 200 && res.data.data.Success) {
			//messageTypeList.value = res.data.data.Datas
			
			res.data.data.Datas.forEach(item => {
				messageTypeList.value.push(item.CTYPE_NAME)
				messageTypeList_origin.value.push(item)
			})
		} 
	}).catch(err => {
		uni.showToast({
			title: ' 修改消息类型状态异常'+err,
			icon: 'none'
		})
	})
}

function onMessageTypeClick(_currentIndex) {
				//debugger
				if (currentMessageType.value !== _currentIndex) {
					currentMessageType.value = _currentIndex
				}
				// 根据类型过滤消息数据
				if(_currentIndex==0){
					messageList.value = copyMessgeList.value.slice();
				}else{
					messageList.value = copyMessgeList.value.filter(item => {
						//return item.CTYPE_ID == _currentIndex
						// 根据下标从类别中获取类型编码，再根据类型编码过滤效果列表
						let _CTYPE_NO = messageTypeList_origin.value[_currentIndex].CTYPE_NO
						//debugger
						return item.CTYPE_NAME == _CTYPE_NO
					})
				}

			}
			
function readMessgeFn(item){
	if(item.CMODULE_URL){
		navigateTo('/pages/common/commonMessage', item)
	
	
	}else{
		// 消息详情页面
		uni.navigateTo({
				url: `/pages/info/infoDetail?title=${item.CTITLE}&CID=${item.CID}`,
			});
	}
	nextTick(()=>{
		let params={
			CID:item.CID,
			CSTATUS:1 //已读
		}
		setMessageStatus(params)
	})
}

// 设置消息状态修改
function setMessageStatus(item){
	let params=item
	serviceApi.updateNoticeStatus(params).then(res => {
		
		if (res && res.data.code === 200 && res.data.data.Success) {
			
		} 
	}).catch(err => {
		uni.showToast({
			title: ' 修改消息状态异常'+err,
			icon: 'none'
		})
	})
}

function onEditDeleteClick(e, item, index) {
	console.log('onEditDeleteClick当前状态：' + e + '，下标：' + index)
	// 单条删除
			let params={
				CID:item.CID,
				CSTATUS:2 //删除
			}
			setMessageStatus(params)
			nextTick(()=>{
				loadDataList()
			})					
			// nextTick(() => {
			// 	if (swipeActionRef.value) {
			// 		swipeActionRef.value.closeAll()
			// 	}
			// })
}
function swipeChange(e, index) {
		//debugger
		console.log('swipeChange当前状态：' + e + '，下标：' + index)
		//editServerItem(item,$event)
	}
function formatDateJS(DATE){
	if(DATE){
		return	dayjs(DATE).format("YY/MM/DD HH:mm")
	}else{
		DATE
	}
	
}
function checkMenuDataBeforeLoad() {
	//debugger
	let _ModuleData = getStorageSync('MESSAGE_LIST')
	if (_ModuleData) {
		loadMessageTypeList()
		messageList.value = _ModuleData
		copyMessgeList.value= _ModuleData
	} else {
		loadMessageTypeList()
		loadDataList()
	}
}

const isScrolling = ref(false)
const scrollTimer = ref(null)
function onScroll() {
	isScrolling.value = true;
	// 2. 清除之前的计时器（避免多次触发时计时混乱）
	if (scrollTimer.value) {
		clearTimeout(scrollTimer.value);
	}

	// 3. 启动新计时器：300ms 后若没有再滚动，视为滚动结束
	scrollTimer.value = setTimeout(() => {
		isScrolling.value = false; // 标记滚动结束
		scrollTimer.value = null; // 清空计时器
		console.log('isScrolling.value:', isScrolling.value)
	}, 500);
}

// 加载菜单数据
function loadDataList() {
	const params = {
		
	}
	uni.showLoading({
		title: '加载中'
	});
	serviceApi.getNotice().then(res => {
		if (res && res.data.code === 200 && res.data.data.Success) {
			setStorageSync('MESSAGE_LIST', res.data.data.Datas)
			messageList.value = res.data.data.Datas
			copyMessgeList.value = res.data.data.Datas
		} else {
			uni.showToast({
				title: res && res.data.data.Content ? res.data.data.Content : '获取信息失败',
				icon: 'none'
			})
		}
	}).catch(err => {
		uni.showToast({
			title: '消息服务异常，请重试',
			icon: 'none'
		})
	}).finally(() => {
		uni.hideLoading();
	})
}


// 搜索消息 CTITLE CCONTENT
function searchAppClick() {
	//debugger
  let val = searchInput.value.trim();
  if (!copyMessgeList.value.length) {
	// 首次搜索时备份原始数据
	copyMessgeList.value = messageList.value.slice();
  }
  if (val) {
	// 过滤 messageList
	messageList.value = copyMessgeList.value.filter(item => {
	  // 支持名称、MD5、类型等字段模糊搜索
	  return (
		(item.CTITLE && item.CTITLE.toLowerCase().includes(val.toLowerCase())) ||
		(item.CCONTENT && item.CCONTENT.toLowerCase().includes(val.toLowerCase())) 
	  );
	});
	
  } else {
	// 搜索框清空，恢复原始数据
	messageList.value = copyMessgeList.value.slice();
	
  }
}

// 保留当前页面，跳转到应用内的某个页面，使用uni.navigateBack可以返回到原页面。
function navigateTo(url, params) {
	console.log("navigateTo===params===", JSON.stringify(params))
	if(!params.text){
		params.text = "测试"
	}
	//在起始页面跳转到test.vue页面并传递参数
	uni.navigateTo({
		url: url,//'test?id=1&name=uniapp'
		success: function (res) {
			// 通过eventChannel向被打开页面传送数据
			res.eventChannel.emit('acceptDataFromOpenerPage', params)
		}
	});
}
function toggleMenu(type) {
	//debugger
		uni.showActionSheet({
			itemList: ['全部已读', '全部删除'],
			success: function(res) {
				let indexNumber= res.tapIndex + 1
				console.log('消息选中了第' + (indexNumber) + '个按钮');
				if(indexNumber==1){
					// 全部已读
					let params ={
						CID:0,
						CSTATUS:1
					}
					setMessageStatus(params)
					nextTick(()=>{
						loadDataList()
					})
				}else if(indexNumber==2){
					uni.showModal({
						title: '提示',
						content: '确定删除全部消息吗？',
						success: function (res) {
							if (res.confirm) {
								console.log('用户点击确定');
								let params ={
									CID:0,
									CSTATUS:2
								}
								setMessageStatus(params)
								nextTick(()=>{
									loadDataList()
								})
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}
				
			},
			fail: function(res) {
				console.log(res.errMsg);
			}
		});
	}

// function longpressItem(index) {
// 	console.log('isScrolling.value:', isScrolling.value)
// 	if (!isScrolling.value) { // 只有不在滚动时，才执行长按逻辑
// 		currentLongPressTab.value = index
// 	}
// }

// function change_Collapse() {
// 	currentLongPressTab.value = null
// }
</script>


<style lang="scss">
.imageItem {
	width: 60rpx;
	height: 60rpx;
}

.textDesc {
	font-size: 24rpx;
	margin-top: 10rpx;
	color: #363636; //#8c8c8c;
}
</style>